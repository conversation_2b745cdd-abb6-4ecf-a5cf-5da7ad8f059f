		/*
			create by zrong.me
			请大家关注我的个人微博 说Z先生爱你
			也请大家关注我的个人网站 zrong.me
		*/
		*{
			padding: 0;
			margin: 0;
			list-style: none;
			text-decoration: none;
		}
		html,body{
			height: 100%;
			width: 100%;
			font-family: 'Helvetica Neue',Helvetica,'PingFang SC','Hiragino Sans GB','Microsoft YaHei',Arial,sans-serif;
			color: #555;
    		font-size: 15px;
    		line-height: 1.7;
    		-webkit-text-size-adjust: 100%;
    		-ms-text-size-adjust: 100%;
    		-webkit-font-smoothing: antialiased;
    		-moz-osx-font-smoothing: grayscale;
		}
		input:focus{
			outline: none;
		}

		/* Touch-friendly improvements */
		* {
			-webkit-tap-highlight-color: transparent;
			-webkit-touch-callout: none;
		}

		button, .telegram-contact, .popup-close-btn {
			-webkit-tap-highlight-color: rgba(0,0,0,0.1);
			touch-action: manipulation;
		}
		canvas{
  			display:block;
  			vertical-align:bottom;
		}
		#box{
			width: 100%;
  			height: 100%;
  			background-color: #F7FAFC;
  			background-image: url('');
  			background-size: cover;
  			background-position: 50% 50%;
  			background-repeat: no-repeat;
  			position: fixed;
  			top: 0;
  			left: 0;
  			right: 0;
  			bottom: 0;
  			z-index: 1;
		}
		/* Main container for layout */
		.main-container {
			display: flex;
			align-items: center;
			justify-content: center;
			min-height: 100vh;
			padding: 20px;
			box-sizing: border-box;
			position: relative;
			z-index: 2;
		}

		/* Advertisement section */
		.advertisement-section {
			flex: 1;
			max-width: 400px;
			margin-right: 60px;
			padding: 30px;
			background: rgba(255, 255, 255, 0.95);
			border-radius: 12px;
			box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
			backdrop-filter: blur(10px);
			border: 1px solid rgba(255, 255, 255, 0.2);
		}

		.ad-content {
			text-align: left;
		}

		.ad-title {
			font-size: 18px;
			font-weight: 600;
			/* color: #2c3e50; */
			color:#871b1b;
			line-height: 1.6;
			margin-bottom: 25px;
			text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
		}

		.telegram-contact {
			display: flex;
			align-items: center;
			padding: 15px 20px;
			background: linear-gradient(135deg, #0088cc, #0099dd);
			border-radius: 8px;
			cursor: pointer;
			transition: all 0.3s ease;
			box-shadow: 0 4px 15px rgba(0, 136, 204, 0.3);
		}

		.telegram-contact:hover {
			transform: translateY(-2px);
			box-shadow: 0 6px 20px rgba(0, 136, 204, 0.4);
			background: linear-gradient(135deg, #0099dd, #00aaee);
		}

		.telegram-icon {
			margin-right: 15px;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 40px;
			height: 40px;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 50%;
		}

		.telegram-icon svg {
			fill: white;
		}

		.telegram-info {
			display: flex;
			flex-direction: column;
			color: white;
		}

		.telegram-label {
			font-size: 14px;
			font-weight: 500;
			opacity: 0.9;
		}

		.telegram-handle {
			font-size: 16px;
			font-weight: 600;
			margin-top: 2px;
		}

		.cent-box{
			width: 300px;
			height: 440px;
			vertical-align: middle;
			white-space: normal;
			margin: 0;
			position: relative;
			z-index: 2;
			background: rgba(255, 255, 255, 0.95);
			border-radius: 12px;
			box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
			backdrop-filter: blur(10px);
			border: 1px solid rgba(255, 255, 255, 0.2);
			padding: 30px;
			box-sizing: border-box;
		}
		.register-box{
			height: 490px;
			margin-top: -270px;
		}
		.cent-box-header{
			text-align: center;
		}
		.hide{
			font: 0/0 a;
    		color: transparent;
    		text-shadow: none;
    		background-color: transparent;
    		border: 0;
		}
		.cent-box-header .main-title{
			width: 160px;
			height: 74px;
			margin: 0 auto;
			background: url('../images/logo1.png') no-repeat;
			background-size: contain;
		}
		.cent-box-header .sub-title{
			margin: 30px 0 20px;
    		font-weight: 400;
    		font-size: 18px;
    		line-height: 1;
		}
		.clearfix:before{
			content: '';
			display: table;
		}
		.index-tab{
			text-align: center;
			font-size: 18px;
			margin-bottom: 10px;
		}
		.index-tab .index-slide-nav{
			display: inline-block;
			position: relative;
		}
		.index-tab .index-slide-nav a{
			float: left;
			width: 4em;
			line-height: 35px;
			opacity: 0.7;
			-webkit-transition: opacity .15s,color .15s;
    		transition: opacity .15s,color .15s;
    		color: #555;
		}
		.index-tab .index-slide-nav a:hover{
			color: #0f88eb：
			opacity: 1;
		}
		.index-tab .index-slide-nav a.active{
			opacity: 1;
			color: #0f88eb;
		}
		.slide-bar{
			position: absolute;
    		left: 0;
    		bottom: 0;
    		margin: 0 .8em;
    		width: 2.4em;
    		height: 2px;
    		background: #0f88eb;
		}
		.slide-bar1{
			left: 4em;
		}
		.form{
			float: none;
			margin: auto;
			text-align: left;
		}
		.form .group{
			padding: 1px 0;
    		border: 1px solid #d5d5d5;
    		border-radius: 3px;
		}
		.form .group .group-ipt{
			position: relative;
    		margin: 0;
    		overflow: hidden;
		}
		.form .group .group-ipt input{
			padding: 1em .8em;
    		width: 100%;
    		box-sizing: border-box;
    		border: 0;
    		border-radius: 0;
    		box-shadow: none;
    		background: rgba(255,255,255,0.5);
    		font-family: 'Microsoft Yahei';
    		color: #666;
    		position: relative;
		}
		#password,#verify,#user,#password1{
			border-top: 1px solid #e8e8e8;
		}
		.imgcode{
			width: 95px;
			position: absolute;
			right: 0;
			top: 2px;
			cursor: pointer;
			height: 40px;
		}
		.button{
			margin-top:18px; 
		}
		#button{
			width: 100%;
			background: #0f88eb;
    		box-shadow: none;
    		border: 0;
    		border-radius: 3px;
    		line-height: 41px;
    		color: #fff;
    		display: block;
    		font-size: 15px;
    		cursor: pointer;
    		font-family: 'Microsoft Yahei';
		}
		#button:hover{
			background: #80c3f7;
		}

		/* Error Popup Styles */
		.popup-overlay {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: rgba(0, 0, 0, 0.6);
			backdrop-filter: blur(5px);
			z-index: 1000;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.popup-container {
			background: white;
			border-radius: 16px;
			box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
			max-width: 400px;
			width: 90%;
			max-height: 90vh;
			overflow: hidden;
			transform: scale(0.7) translateY(50px);
			opacity: 0;
			transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
		}

		.popup-container.popup-show {
			transform: scale(1) translateY(0);
			opacity: 1;
		}

		.popup-content {
			padding: 40px 30px 30px;
			text-align: center;
		}

		.popup-icon {
			margin-bottom: 20px;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.popup-icon svg {
			filter: drop-shadow(0 4px 8px rgba(255, 71, 87, 0.3));
		}

		.popup-title {
			font-size: 24px;
			font-weight: 600;
			color: #2c3e50;
			margin-bottom: 15px;
			text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
		}

		.popup-message {
			font-size: 16px;
			color: #555;
			line-height: 1.6;
			margin-bottom: 30px;
			font-weight: 400;
		}

		.popup-close-btn {
			background: linear-gradient(135deg, #ff6b6b, #ee5a52);
			color: white;
			border: none;
			border-radius: 8px;
			padding: 12px 30px;
			font-size: 16px;
			font-weight: 600;
			cursor: pointer;
			transition: all 0.3s ease;
			box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
			min-width: 100px;
		}

		.popup-close-btn:hover {
			background: linear-gradient(135deg, #ee5a52, #dd4b4b);
			transform: translateY(-2px);
			box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
		}

		.popup-close-btn:active {
			transform: translateY(0);
		}
		.remember{
			margin-top: 10px;
			line-height: 30px;
		}
		.remember label{
			display: block;
		}
		.remember-me{
			font-size: 14px;
			float: left;
			position: relative;
			cursor: pointer;
		}
		.icon{
			width: 11px;
			height: 11px;
			display: block;
			border: 1px solid #ccc;
			float: left;
			margin-top: 8px;
			margin-right: 5px;
			cursor: pointer;
		}
		.zt{
			width: 9px;
			height: 9px;
			background: #0f88eb;
			margin: 1px;
			display: block;
		}
		#remember-me{
			position: absolute;
			left: 0;
			top: 8px;
			opacity: 0;
			cursor: pointer;
		}
		.forgot-password{
			float: right;
			font-size: 14px;
		}
		.forgot-password a{
			color: #555;
		}
		.forgot-password a:hover{
			text-decoration: underline;
		}
		.footer{
			position: fixed;
			width: 100%;
			height: 40px;
			bottom: 0;
			left: 0;
			text-align: center;
			color: #999;
			z-index: 2;
			padding-bottom: 10px;
			font-size: 13px;
		}
		.footer a{
			color: #666;
			text-decoration: underline;
		}

		/* Responsive design */
		@media (max-width: 1024px) {
			.main-container {
				padding: 15px;
				gap: 30px;
			}

			.advertisement-section {
				margin-right: 40px;
				max-width: 350px;
			}

			.cent-box {
				max-width: 320px;
			}
		}

		@media (max-width: 768px) {
			.main-container {
				flex-direction: column;
				padding: 15px;
				gap: 25px;
				min-height: 100vh;
				justify-content: flex-start;
				padding-top: 40px;
			}

			.advertisement-section {
				margin-right: 0;
				margin-bottom: 0;
				max-width: 100%;
				order: 1;
			}

			.cent-box {
				width: 100%;
				max-width: 400px;
				height: auto;
				min-height: auto;
				order: 2;
				margin: 0 auto;
			}

			.ad-title {
				font-size: 16px;
				line-height: 1.5;
			}

			.telegram-contact {
				padding: 14px 18px;
			}

			.telegram-icon {
				width: 36px;
				height: 36px;
				margin-right: 14px;
			}

			.telegram-label {
				font-size: 13px;
			}

			.telegram-handle {
				font-size: 15px;
			}

			/* Adjust form elements for tablet */
			.form .group .group-ipt input {
				padding: 1.2em 1em;
				font-size: 16px; /* Prevents zoom on iOS */
			}

			#button {
				line-height: 45px;
				font-size: 16px;
			}
		}

		@media (max-width: 480px) {
			.main-container {
				padding: 10px;
				padding-top: 20px;
				gap: 20px;
			}

			.advertisement-section {
				padding: 20px 15px;
				margin-bottom: 0;
			}

			.cent-box {
				padding: 25px 20px;
				width: 100%;
				max-width: 100%;
				margin: 0;
			}

			.ad-title {
				font-size: 14px;
				line-height: 1.4;
				margin-bottom: 20px;
			}

			.telegram-contact {
				padding: 12px 15px;
				border-radius: 6px;
			}

			.telegram-icon {
				width: 32px;
				height: 32px;
				margin-right: 12px;
			}

			.telegram-label {
				font-size: 12px;
			}

			.telegram-handle {
				font-size: 14px;
			}

			/* Mobile form optimizations */
			.form {
				width: 100%;
			}

			.form .group .group-ipt input {
				padding: 1em 0.8em;
				font-size: 16px; /* Prevents zoom on iOS */
				-webkit-appearance: none;
				border-radius: 0;
			}

			#button {
				line-height: 42px;
				font-size: 16px;
				border-radius: 6px;
				-webkit-appearance: none;
			}

			.remember {
				margin-top: 15px;
			}

			.remember-me {
				font-size: 13px;
			}

			/* Header adjustments */
			.cent-box-header .sub-title {
				font-size: 16px;
				margin: 20px 0 15px;
			}

			.cent-box-header svg {
				width: 48px;
				height: 48px;
			}

			/* Popup mobile optimizations */
			.popup-container {
				width: 95%;
				margin: 0 10px;
			}

			.popup-content {
				padding: 25px 20px 20px;
			}

			.popup-title {
				font-size: 18px;
				margin-bottom: 12px;
			}

			.popup-message {
				font-size: 14px;
				margin-bottom: 25px;
			}

			.popup-close-btn {
				padding: 12px 30px;
				font-size: 15px;
				border-radius: 6px;
			}

			.popup-icon svg {
				width: 40px;
				height: 40px;
			}
		}

		/* Extra small screens */
		@media (max-width: 360px) {
			.main-container {
				padding: 8px;
				padding-top: 15px;
			}

			.advertisement-section {
				padding: 15px 12px;
			}

			.cent-box {
				padding: 20px 15px;
			}

			.ad-title {
				font-size: 13px;
				line-height: 1.3;
			}

			.telegram-contact {
				padding: 10px 12px;
			}

			.form .group .group-ipt input {
				padding: 0.9em 0.7em;
			}

			.popup-content {
				padding: 20px 15px 18px;
			}
		}
