<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=0.5, maximum-scale=2.0, user-scalable=no" />
    <title>后台登录</title>
    <link rel="stylesheet" type="text/css" href="demo/style/register-login.css">
    <script src="demo/js/jquery.min.js"></script>
</head>
<body>
    <div id="box"></div>
    <div class="main-container">
        <!-- Advertisement Section -->
        <div class="advertisement-section">
            <div class="ad-content">
                <h3 class="ad-title">寻海外App（Facebook Tiktok Instagram 刷粉丝点赞数量）逆向协议技术供货有销售后台，有稳定订单！销量每天10万美金+</h3>
                <div class="telegram-contact" onclick="window.open('https://t.me/your_telegram_account', '_blank')">
                    <div class="telegram-icon">
                        <svg viewBox="0 0 24 24" width="24" height="24" fill="#0088cc">
                            <path d="M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16c-.169 1.858-.896 6.728-.896 6.728-.377 2.655-.377 2.655-1.377 2.655-.896 0-1.209-.585-1.209-1.377v-4.326s-3.624 2.332-4.265 2.705c-.585.341-1.209.226-1.209-.452v-2.005s4.891-4.891 5.249-5.249c.226-.226.113-.339-.113-.113L5.8 11.124s-.905-.339-1.018-1.018c-.113-.679.452-1.131.452-1.131s7.91-2.995 8.816-3.336c.905-.34 1.518.227 1.518 1.521z"/>
                        </svg>
                    </div>
                    <div class="telegram-info">
                        <span class="telegram-label">联系我们</span>
                        <span class="telegram-handle">@SocialMusk</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Login Section -->
        <div class="cent-box">
            <div class="cent-box-header">
                <svg t="" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="946" width="64" height="64">                <path d="M298.666667 896c141.397333 0 256-115.968 256-259.029333C554.666667 541.568 469.333333 371.925333 298.666667 128c-170.666667 243.925333-256 413.568-256 508.970667C42.666667 780.032 157.269333 896 298.666667 896z" fill="#FFCCCC" p-id="947"></path><path d="M554.666667 85.333333c235.648 0 426.666667 191.018667 426.666666 426.666667s-191.018667 426.666667-426.666666 426.666667c-66.986667 0-131.84-15.488-190.464-44.8a42.666667 42.666667 0 0 1 38.144-76.330667 341.333333 341.333333 0 1 0 1.834666-612.010667 42.666667 42.666667 0 1 1-37.717333-76.544A425.088 425.088 0 0 1 554.666667 85.333333z m0 256a170.666667 170.666667 0 1 1 0 341.333334 170.666667 170.666667 0 0 1 0-341.333334z m0 85.333334a85.333333 85.333333 0 1 0 0 170.666666 85.333333 85.333333 0 0 0 0-170.666666z" fill="#363637" p-id="948"></path></svg>

                <h2 class="sub-title">客户登录</h2>
            </div>

            <div class="cont-main clearfix">
                <div class="index-tab">
                    <div class="index-slide-nav">
                        <a class="active">登录</a>
                        <!--<a href="register.html">注册</a>-->
                        <div class="slide-bar"></div>
                    </div>
                </div>

                <div class="login form">
                    <div class="group">
                        <div class="group-ipt email">
                            <input type="text" name="username" id="username" class="ipt" placeholder="账号" required="">
                        </div>
                        <div class="group-ipt password">
                            <input type="password" name="password" id="password" class="ipt" placeholder="密码" required="">
                        </div>
                    </div>
                </div>

                <div class="button">
                    <button type="button" class="login-btn register-btn" id="button">登录</button>
                </div>



                <div class="remember clearfix">
                    <label class="remember-me">
                        <span class="icon"><span class="zt"></span></span>
                        <input type="checkbox" name="remember-me" id="remember-me" class="remember-mecheck" checked="">记住我
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        <p>Designed By HangTong & DT</p>
    </div>

    <!-- Error Popup Modal -->
    <div id="error-popup" class="popup-overlay" style="display: none;">
        <div class="popup-container">
            <div class="popup-content">
                <div class="popup-icon">
                    <svg viewBox="0 0 24 24" width="48" height="48" fill="#ff4757">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM13 17h-2v-2h2v2zm0-4h-2V7h2v6z"/>
                    </svg>
                </div>
                <h3 class="popup-title">登录失败</h3>
                <p class="popup-message">账号或密码错误请联系客服!</p>
                <button class="popup-close-btn" onclick="closeErrorPopup()">确定</button>
            </div>
        </div>
    </div>

    <script src='demo/js/particles.js' type="text/javascript"></script>
    <script src='demo/js/background.js' type="text/javascript"></script>


    <script>
        $("#button").on("click", function () {
            // Show error popup immediately
            showErrorPopup();
        });

        function showErrorPopup() {
            $("#error-popup").fadeIn(300);
            $(".popup-container").addClass("popup-show");
        }

        function closeErrorPopup() {
            $(".popup-container").removeClass("popup-show");
            setTimeout(function() {
                $("#error-popup").fadeOut(300);
            }, 200);
        }

        // Close popup when clicking outside the content
        $("#error-popup").on("click", function(e) {
            if (e.target === this) {
                closeErrorPopup();
            }
        });
    </script>

</body>




</html>
